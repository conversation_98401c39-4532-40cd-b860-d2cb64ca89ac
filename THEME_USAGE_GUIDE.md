# Dark Mode Theme System - Usage Guide

## Overview

This guide provides comprehensive documentation for the robust dark mode theme system implemented in your React Native app. The system uses React Context API for state management, MMKV for persistence, and provides a complete set of themed components and utilities.

## Quick Start

### 1. Basic Usage

The theme system is automatically available throughout your app once integrated. Here's how to use it:

```tsx
import React from 'react';
import {ThemedView, ThemedText, useTheme} from '@/components/themed';

const MyComponent: React.FC = () => {
  const {theme, isDark, toggleTheme} = useTheme();

  return (
    <ThemedView variant="container">
      <ThemedText variant="heading1">
        Current theme: {isDark ? 'Dark' : 'Light'}
      </ThemedText>
      <ThemedButton onPress={toggleTheme}>
        Toggle Theme
      </ThemedButton>
    </ThemedView>
  );
};
```

### 2. Using Themed Components

```tsx
import {
  ThemedView,
  ThemedText,
  ThemedButton,
} from '@/components/themed';

const ExampleScreen: React.FC = () => {
  return (
    <ThemedView variant="container">
      {/* Typography */}
      <ThemedText variant="heading1">Main Title</ThemedText>
      <ThemedText variant="body" color="textSecondary">
        Subtitle text
      </ThemedText>

      {/* Buttons */}
      <ThemedButton variant="primary" size="large">
        Primary Action
      </ThemedButton>
      <ThemedButton variant="outline" size="medium">
        Secondary Action
      </ThemedButton>

      {/* Cards */}
      <ThemedView variant="card" elevation={2}>
        <ThemedText variant="heading3">Card Title</ThemedText>
        <ThemedText variant="body">Card content</ThemedText>
      </ThemedView>
    </ThemedView>
  );
};
```

### 3. Custom Styled Components

```tsx
import {useThemedStyles} from '@/hooks/useThemedStyles';

const CustomComponent: React.FC = () => {
  const styles = useThemedStyles((theme) => ({
    container: {
      backgroundColor: theme.colors.surface,
      padding: theme.spacing.lg,
      borderRadius: theme.layout.borderRadius.md,
      shadowColor: theme.colors.shadow,
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    title: {
      fontSize: theme.typography.lg,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: theme.spacing.md,
    },
    description: {
      fontSize: theme.typography.sm,
      color: theme.colors.textSecondary,
      lineHeight: theme.typography.sm * 1.4,
    },
  }));

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Custom Component</Text>
      <Text style={styles.description}>
        This component uses themed styles
      </Text>
    </View>
  );
};
```

## Advanced Usage

### 1. Theme Mode Management

```tsx
import {useTheme} from '@/contexts/ThemeContext';

const ThemeSettings: React.FC = () => {
  const {themeMode, setThemeMode, colorScheme} = useTheme();

  return (
    <View>
      <Text>Current setting: {themeMode}</Text>
      <Text>Active scheme: {colorScheme}</Text>
      
      <Button 
        title="Light Mode" 
        onPress={() => setThemeMode('light')} 
      />
      <Button 
        title="Dark Mode" 
        onPress={() => setThemeMode('dark')} 
      />
      <Button 
        title="System Mode" 
        onPress={() => setThemeMode('system')} 
      />
    </View>
  );
};
```

### 2. Conditional Styling

```tsx
import {createConditionalStyle, useThemedStyles} from '@/hooks/useThemedStyles';

const ConditionalComponent: React.FC = () => {
  const styles = useThemedStyles((theme) => ({
    container: createConditionalStyle(
      // Light mode style
      {
        backgroundColor: '#ffffff',
        borderColor: '#e0e0e0',
      },
      // Dark mode style
      {
        backgroundColor: '#1a1a1a',
        borderColor: '#333333',
      }
    )(theme),
    text: {
      color: theme.colors.text,
      fontSize: theme.typography.sm,
    },
  }));

  return (
    <View style={styles.container}>
      <Text style={styles.text}>Conditional styling</Text>
    </View>
  );
};
```

### 3. Color Utilities

```tsx
import {withOpacity, useThemeColors} from '@/hooks/useThemedStyles';

const ColorUtilsExample: React.FC = () => {
  const colors = useThemeColors();

  const styles = {
    overlay: {
      backgroundColor: withOpacity(colors.background, 0.8),
    },
    border: {
      borderColor: withOpacity(colors.primary, 0.3),
      borderWidth: 1,
    },
  };

  return (
    <View style={styles.overlay}>
      <View style={styles.border}>
        <Text>Using color utilities</Text>
      </View>
    </View>
  );
};
```

## Navigation Integration

The theme system automatically integrates with React Navigation:

```tsx
// Navigation themes are applied automatically
// Header styles adapt to the current theme
// Tab bar colors change with theme

// For custom header styling:
import {createThemedHeaderStyle} from '@/navigation/utils';

const screenOptions = ({theme}) => ({
  ...createThemedHeaderStyle(theme.colors),
  headerTitle: 'My Screen',
});
```

## Best Practices

### 1. Performance Optimization

- Use `useThemedStyles` for component-specific styles
- Memoize style factories when possible
- Avoid inline style objects in render methods

### 2. Consistent Color Usage

```tsx
// ✅ Good - Use semantic color names
backgroundColor: theme.colors.surface
color: theme.colors.textSecondary

// ❌ Avoid - Don't use hardcoded colors
backgroundColor: '#ffffff'
color: '#666666'
```

### 3. Responsive Design

```tsx
const styles = useThemedStyles((theme) => ({
  container: {
    padding: theme.spacing.lg,
    // Use theme spacing for consistency
    marginHorizontal: theme.layout.ph.screen,
  },
  text: {
    fontSize: theme.typography.sm,
    // Use theme typography scales
  },
}));
```

## Extending the Theme System

### 1. Adding New Colors

```tsx
// In src/styles/theme.ts
export const lightThemeColors: ThemeColors = {
  // ... existing colors
  customColor: '#your-color',
  customSecondary: '#another-color',
};

export const darkThemeColors: ThemeColors = {
  // ... existing colors
  customColor: '#your-dark-color',
  customSecondary: '#another-dark-color',
};
```

### 2. Creating New Themed Components

```tsx
// src/components/themed/ThemedCustom.tsx
import React, {forwardRef} from 'react';
import {View, ViewProps} from 'react-native';
import {useTheme} from '@/contexts/ThemeContext';

interface ThemedCustomProps extends ViewProps {
  variant?: 'default' | 'special';
}

export const ThemedCustom = forwardRef<View, ThemedCustomProps>(
  ({variant = 'default', style, ...props}, ref) => {
    const {theme} = useTheme();

    const baseStyle = {
      backgroundColor: variant === 'special' 
        ? theme.colors.accent 
        : theme.colors.surface,
      padding: theme.spacing.md,
      borderRadius: theme.layout.borderRadius.sm,
    };

    return (
      <View 
        ref={ref} 
        style={[baseStyle, style]} 
        {...props} 
      />
    );
  },
);
```

## Demo Screen

A complete demo screen is available at `src/screens/ThemeDemoScreen.tsx` that showcases:

- Theme mode switching (light/dark/system)
- All themed components
- Typography variants
- Button variants and sizes
- Color palette display
- Interactive elements

To add it to your navigation:

```tsx
// In your navigator
<Stack.Screen 
  name="ThemeDemo" 
  component={ThemeDemoScreen}
  options={{title: 'Theme Demo'}}
/>
```

## Troubleshooting

### Common Issues

1. **Theme not updating**: Ensure ThemeProvider wraps your app
2. **Colors not changing**: Check if you're using theme colors vs hardcoded values
3. **Performance issues**: Use memoized style factories
4. **TypeScript errors**: Ensure proper imports and type definitions

### Migration from Existing Styles

1. Replace hardcoded colors with theme colors
2. Use themed components instead of basic RN components
3. Convert StyleSheet.create to useThemedStyles
4. Update navigation styles to use theme integration

This theme system provides a solid foundation for scalable, maintainable dark mode support in your React Native application.
