import React, {forwardRef} from 'react';
import {
  TouchableOpacity,
  TouchableOpacityProps,
  ViewStyle,
  ActivityIndicator,
} from 'react-native';
import {useTheme} from '@/contexts/ThemeContext';
import {ThemeColors} from '@/styles/theme';
import {ThemedText, TextColor} from './ThemedText';
import {createShadowStyle, withOpacity} from '@/hooks/useThemedStyles';

/* ============================================================================================== */
/*                                              TYPES                                             */
/* ============================================================================================== */

type ButtonVariant = 
  | 'primary'
  | 'secondary'
  | 'outline'
  | 'ghost'
  | 'danger';

type ButtonSize = 'small' | 'medium' | 'large';

interface ThemedButtonProps extends Omit<TouchableOpacityProps, 'style'> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  loading?: boolean;
  disabled?: boolean;
  style?: ViewStyle | ViewStyle[];
  children: React.ReactNode;
}

/* ============================================================================================== */
/*                                            COMPONENT                                           */
/* ============================================================================================== */

export const ThemedButton = forwardRef<TouchableOpacity, ThemedButtonProps>(
  ({
    variant = 'primary',
    size = 'medium',
    loading = false,
    disabled = false,
    style,
    children,
    ...props
  }, ref) => {
    const {theme} = useTheme();

    const isDisabled = disabled || loading;

    const getSizeStyle = (size: ButtonSize): ViewStyle => {
      switch (size) {
        case 'small':
          return {
            paddingHorizontal: theme.spacing.md,
            paddingVertical: theme.spacing.sm,
            borderRadius: theme.layout.borderRadius.sm,
            minHeight: 36,
          };
        case 'medium':
          return {
            paddingHorizontal: theme.spacing.lg,
            paddingVertical: theme.spacing.md,
            borderRadius: theme.layout.borderRadius.sm,
            minHeight: 44,
          };
        case 'large':
          return {
            paddingHorizontal: theme.spacing.xl,
            paddingVertical: theme.spacing.lg,
            borderRadius: theme.layout.borderRadius.md,
            minHeight: 52,
          };
        default:
          return {};
      }
    };

    const getVariantStyle = (variant: ButtonVariant): {
      buttonStyle: ViewStyle;
      textColor: TextColor;
    } => {
      switch (variant) {
        case 'primary':
          return {
            buttonStyle: {
              backgroundColor: isDisabled 
                ? theme.colors.disabled 
                : theme.colors.primary,
              ...createShadowStyle(2)(theme),
            },
            textColor: 'textInverse',
          };
        case 'secondary':
          return {
            buttonStyle: {
              backgroundColor: isDisabled 
                ? theme.colors.disabled 
                : theme.colors.secondary,
              ...createShadowStyle(1)(theme),
            },
            textColor: 'textInverse',
          };
        case 'outline':
          return {
            buttonStyle: {
              backgroundColor: 'transparent',
              borderWidth: 1,
              borderColor: isDisabled 
                ? theme.colors.disabled 
                : theme.colors.primary,
            },
            textColor: isDisabled ? 'disabled' : 'primary',
          };
        case 'ghost':
          return {
            buttonStyle: {
              backgroundColor: isDisabled
                ? withOpacity(theme.colors.disabled, 0.1)
                : withOpacity(theme.colors.primary, 0.1),
            },
            textColor: isDisabled ? 'disabled' : 'primary',
          };
        case 'danger':
          return {
            buttonStyle: {
              backgroundColor: isDisabled 
                ? theme.colors.disabled 
                : theme.colors.error,
              ...createShadowStyle(2)(theme),
            },
            textColor: 'textInverse',
          };
        default:
          return {
            buttonStyle: {},
            textColor: 'text',
          };
      }
    };

    const {buttonStyle: variantStyle, textColor} = getVariantStyle(variant);
    const sizeStyle = getSizeStyle(size);

    const buttonStyle: ViewStyle = {
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      ...sizeStyle,
      ...variantStyle,
    };

    const combinedStyle = [buttonStyle, style];

    const renderContent = (): React.ReactNode => {
      if (loading) {
        return (
          <>
            <ActivityIndicator 
              size="small" 
              color={theme.colors[textColor] || theme.colors.text}
              style={{marginRight: theme.spacing.sm}}
            />
            <ThemedText variant="button" color={textColor}>
              {children}
            </ThemedText>
          </>
        );
      }

      return (
        <ThemedText variant="button" color={textColor}>
          {children}
        </ThemedText>
      );
    };

    return (
      <TouchableOpacity
        ref={ref}
        style={combinedStyle}
        disabled={isDisabled}
        activeOpacity={0.7}
        {...props}
      >
        {renderContent()}
      </TouchableOpacity>
    );
  },
);

ThemedButton.displayName = 'ThemedButton';

/* ============================================================================================== */
/*                                             EXPORTS                                            */
/* ============================================================================================== */

export type {ThemedButtonProps, ButtonVariant, ButtonSize};
