import React, {forwardRef} from 'react';
import {Text, TextProps, TextStyle} from 'react-native';
import {useTheme} from '@/contexts/ThemeContext';
import {ThemeColors} from '@/styles/theme';

/* ============================================================================================== */
/*                                              TYPES                                             */
/* ============================================================================================== */

type TextVariant = 
  | 'heading1'
  | 'heading2' 
  | 'heading3'
  | 'body'
  | 'bodySmall'
  | 'caption'
  | 'button'
  | 'label';

type TextColor = keyof ThemeColors | 'inherit';

interface ThemedTextProps extends Omit<TextProps, 'style'> {
  variant?: TextVariant;
  color?: TextColor;
  style?: TextStyle | TextStyle[];
  children: React.ReactNode;
}

/* ============================================================================================== */
/*                                            COMPONENT                                           */
/* ============================================================================================== */

export const ThemedText = forwardRef<Text, ThemedTextProps>(
  ({variant = 'body', color = 'text', style, children, ...props}, ref) => {
    const {theme} = useTheme();

    const getVariantStyle = (variant: TextVariant): TextStyle => {
      switch (variant) {
        case 'heading1':
          return {
            fontSize: theme.typography.xxl,
            fontWeight: '700',
            lineHeight: theme.typography.xxl * 1.2,
          };
        case 'heading2':
          return {
            fontSize: theme.typography.xl,
            fontWeight: '600',
            lineHeight: theme.typography.xl * 1.2,
          };
        case 'heading3':
          return {
            fontSize: theme.typography.lg,
            fontWeight: '600',
            lineHeight: theme.typography.lg * 1.2,
          };
        case 'body':
          return {
            fontSize: theme.typography.sm,
            fontWeight: '400',
            lineHeight: theme.typography.sm * 1.4,
          };
        case 'bodySmall':
          return {
            fontSize: theme.typography.xs,
            fontWeight: '400',
            lineHeight: theme.typography.xs * 1.4,
          };
        case 'caption':
          return {
            fontSize: theme.typography.xs,
            fontWeight: '500',
            lineHeight: theme.typography.xs * 1.3,
          };
        case 'button':
          return {
            fontSize: theme.typography.sm,
            fontWeight: '600',
            lineHeight: theme.typography.sm * 1.2,
          };
        case 'label':
          return {
            fontSize: theme.typography.xs,
            fontWeight: '500',
            lineHeight: theme.typography.xs * 1.2,
            textTransform: 'uppercase',
            letterSpacing: 0.5,
          };
        default:
          return {};
      }
    };

    const getTextColor = (color: TextColor): string => {
      if (color === 'inherit') {
        return 'inherit';
      }
      return theme.colors[color] || theme.colors.text;
    };

    const textStyle: TextStyle = {
      fontFamily: theme.fonts.default,
      color: getTextColor(color),
      ...getVariantStyle(variant),
    };

    const combinedStyle = [textStyle, style];

    return (
      <Text ref={ref} style={combinedStyle} {...props}>
        {children}
      </Text>
    );
  },
);

ThemedText.displayName = 'ThemedText';

/* ============================================================================================== */
/*                                             EXPORTS                                            */
/* ============================================================================================== */

export type {ThemedTextProps, TextVariant, TextColor};
