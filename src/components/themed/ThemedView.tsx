import React, {forwardRef} from 'react';
import {View, ViewProps, ViewStyle} from 'react-native';
import {useTheme} from '@/contexts/ThemeContext';
import {ThemeColors} from '@/styles/theme';
import {createShadowStyle} from '@/hooks/useThemedStyles';

/* ============================================================================================== */
/*                                              TYPES                                             */
/* ============================================================================================== */

type ViewVariant = 
  | 'container'
  | 'surface'
  | 'card'
  | 'overlay'
  | 'transparent';

type ViewColor = keyof ThemeColors | 'transparent';

interface ThemedViewProps extends Omit<ViewProps, 'style'> {
  variant?: ViewVariant;
  backgroundColor?: ViewColor;
  borderColor?: ViewColor;
  elevation?: number;
  style?: ViewStyle | ViewStyle[];
  children?: React.ReactNode;
}

/* ============================================================================================== */
/*                                            COMPONENT                                           */
/* ============================================================================================== */

export const ThemedView = forwardRef<View, ThemedViewProps>(
  ({
    variant = 'transparent',
    backgroundColor,
    borderColor,
    elevation = 0,
    style,
    children,
    ...props
  }, ref) => {
    const {theme} = useTheme();

    const getVariantStyle = (variant: ViewVariant): ViewStyle => {
      switch (variant) {
        case 'container':
          return {
            flex: 1,
            backgroundColor: theme.colors.background,
          };
        case 'surface':
          return {
            backgroundColor: theme.colors.surface,
            borderRadius: theme.layout.borderRadius.md,
          };
        case 'card':
          return {
            backgroundColor: theme.colors.surface,
            borderRadius: theme.layout.borderRadius.md,
            padding: theme.spacing.lg,
            marginVertical: theme.spacing.sm,
          };
        case 'overlay':
          return {
            backgroundColor: theme.colors.overlay,
          };
        case 'transparent':
        default:
          return {};
      }
    };

    const getBackgroundColor = (color: ViewColor): string => {
      if (color === 'transparent') {
        return 'transparent';
      }
      return theme.colors[color] || 'transparent';
    };

    const getBorderColor = (color: ViewColor): string => {
      if (color === 'transparent') {
        return 'transparent';
      }
      return theme.colors[color] || theme.colors.border;
    };

    const baseStyle: ViewStyle = {
      ...getVariantStyle(variant),
    };

    // Apply custom background color if provided
    if (backgroundColor) {
      baseStyle.backgroundColor = getBackgroundColor(backgroundColor);
    }

    // Apply custom border color if provided
    if (borderColor) {
      baseStyle.borderColor = getBorderColor(borderColor);
      baseStyle.borderWidth = baseStyle.borderWidth || 1;
    }

    // Apply elevation/shadow if provided
    const shadowStyle = elevation > 0 ? createShadowStyle(elevation)(theme) : {};

    const combinedStyle = [baseStyle, shadowStyle, style];

    return (
      <View ref={ref} style={combinedStyle} {...props}>
        {children}
      </View>
    );
  },
);

ThemedView.displayName = 'ThemedView';

/* ============================================================================================== */
/*                                             EXPORTS                                            */
/* ============================================================================================== */

export type {ThemedViewProps, ViewVariant, ViewColor};
