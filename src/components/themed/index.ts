/* ============================================================================================== */
/*                                        THEMED COMPONENTS                                       */
/* ============================================================================================== */

export {ThemedText} from './ThemedText';
export type {ThemedTextProps, TextVariant, TextColor} from './ThemedText';

export {ThemedView} from './ThemedView';
export type {ThemedViewProps, ViewVariant, ViewColor} from './ThemedView';

export {ThemedButton} from './ThemedButton';
export type {ThemedButtonProps, ButtonVariant, ButtonSize} from './ThemedButton';

/* ============================================================================================== */
/*                                        RE-EXPORTS                                              */
/* ============================================================================================== */

// Re-export theme context and hooks for convenience
export {useTheme, ThemeProvider} from '@/contexts/ThemeContext';
export type {ThemeContextValue, ThemeProviderProps} from '@/contexts/ThemeContext';

export {
  useThemedStyles,
  useThemeColors,
  createThemedStyles,
  createConditionalStyle,
  createColoredStyle,
  withOpacity,
  createShadowStyle,
  commonThemedStyles,
} from '@/hooks/useThemedStyles';

export type {
  NamedStyles,
  StylesFactory,
  ThemedStylesHook,
} from '@/hooks/useThemedStyles';
