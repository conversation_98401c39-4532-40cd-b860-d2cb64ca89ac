import React, {createContext, useContext, useEffect, useMemo, useState, ReactNode} from 'react';
import {useColorScheme, StatusBar} from 'react-native';
import {storage} from '@/storage';
import {
  Theme,
  ThemeMode,
  ColorScheme,
  lightTheme,
  darkTheme,
  getTheme,
} from '@/styles/theme';

/* ============================================================================================== */
/*                                              TYPES                                             */
/* ============================================================================================== */

interface ThemeContextValue {
  theme: Theme;
  themeMode: ThemeMode;
  colorScheme: ColorScheme;
  isDark: boolean;
  setThemeMode: (mode: ThemeMode) => void;
  toggleTheme: () => void;
}

interface ThemeProviderProps {
  children: ReactNode;
}

/* ============================================================================================== */
/*                                            CONSTANTS                                           */
/* ============================================================================================== */

const THEME_STORAGE_KEY = 'user_theme_preference';
const DEFAULT_THEME_MODE: ThemeMode = 'system';

/* ============================================================================================== */
/*                                             CONTEXT                                            */
/* ============================================================================================== */

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

/* ============================================================================================== */
/*                                             PROVIDER                                           */
/* ============================================================================================== */

export const ThemeProvider: React.FC<ThemeProviderProps> = ({children}) => {
  const systemColorScheme = useColorScheme();
  const [themeMode, setThemeModeState] = useState<ThemeMode>(DEFAULT_THEME_MODE);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize theme from storage
  useEffect(() => {
    const initializeTheme = async (): Promise<void> => {
      try {
        const storedTheme = storage.getString(THEME_STORAGE_KEY) as ThemeMode | undefined;
        if (storedTheme && ['light', 'dark', 'system'].includes(storedTheme)) {
          setThemeModeState(storedTheme);
        }
      } catch (error) {
        console.error('Failed to load theme preference:', error);
      } finally {
        setIsInitialized(true);
      }
    };

    initializeTheme();
  }, []);

  // Determine current color scheme
  const colorScheme: ColorScheme = useMemo(() => {
    if (themeMode === 'system') {
      return systemColorScheme === 'dark' ? 'dark' : 'light';
    }
    return themeMode as ColorScheme;
  }, [themeMode, systemColorScheme]);

  // Get current theme
  const theme: Theme = useMemo(() => {
    return getTheme(colorScheme);
  }, [colorScheme]);

  // Set theme mode with persistence
  const setThemeMode = useMemo(
    () => (mode: ThemeMode): void => {
      try {
        setThemeModeState(mode);
        storage.set(THEME_STORAGE_KEY, mode);
      } catch (error) {
        console.error('Failed to save theme preference:', error);
      }
    },
    [],
  );

  // Toggle between light and dark (ignores system)
  const toggleTheme = useMemo(
    () => (): void => {
      const newMode = colorScheme === 'dark' ? 'light' : 'dark';
      setThemeMode(newMode);
    },
    [colorScheme, setThemeMode],
  );

  // Context value with memoization for performance
  const contextValue: ThemeContextValue = useMemo(
    () => ({
      theme,
      themeMode,
      colorScheme,
      isDark: colorScheme === 'dark',
      setThemeMode,
      toggleTheme,
    }),
    [theme, themeMode, colorScheme, setThemeMode, toggleTheme],
  );

  // Update StatusBar when theme changes
  useEffect(() => {
    if (isInitialized) {
      StatusBar.setBarStyle(
        colorScheme === 'dark' ? 'light-content' : 'dark-content',
        true,
      );
    }
  }, [colorScheme, isInitialized]);

  // Don't render until theme is initialized to prevent flash
  if (!isInitialized) {
    return null;
  }

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

/* ============================================================================================== */
/*                                              HOOK                                              */
/* ============================================================================================== */

export const useTheme = (): ThemeContextValue => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

/* ============================================================================================== */
/*                                             EXPORTS                                            */
/* ============================================================================================== */

export type {ThemeContextValue, ThemeProviderProps};
export {ThemeContext};
