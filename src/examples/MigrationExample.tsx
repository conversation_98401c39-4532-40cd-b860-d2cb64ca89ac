/* ============================================================================================== */
/*                                      MIGRATION EXAMPLE                                         */
/* ============================================================================================== */

// This file shows how to migrate from your existing styling approach to the new theme system

import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {
  ThemedView,
  ThemedText,
  ThemedButton,
  useThemedStyles,
  useTheme,
} from '@/components/themed';

/* ============================================================================================== */
/*                                         BEFORE (OLD)                                          */
/* ============================================================================================== */

// Old approach using hardcoded colors and GlobalStyles
const OldComponent: React.FC = () => {
  return (
    <View style={oldStyles.container}>
      <Text style={oldStyles.title}>Welcome to Assetify</Text>
      <Text style={oldStyles.subtitle}>Manage your crypto assets</Text>
      
      <View style={oldStyles.card}>
        <Text style={oldStyles.cardTitle}>Portfolio Balance</Text>
        <Text style={oldStyles.balance}>$12,345.67</Text>
      </View>

      <TouchableOpacity style={oldStyles.button}>
        <Text style={oldStyles.buttonText}>View Portfolio</Text>
      </TouchableOpacity>
    </View>
  );
};

const oldStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF', // Hardcoded color
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#000000', // Hardcoded color
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666', // Hardcoded color
    marginBottom: 24,
  },
  card: {
    backgroundColor: '#F5F5F5', // Hardcoded color
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#888888', // Hardcoded color
    marginBottom: 8,
  },
  balance: {
    fontSize: 28,
    fontWeight: '700',
    color: '#633c61', // Brand color hardcoded
  },
  button: {
    backgroundColor: '#633c61', // Hardcoded color
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#FFFFFF', // Hardcoded color
    fontSize: 16,
    fontWeight: '600',
  },
});

/* ============================================================================================== */
/*                                         AFTER (NEW)                                           */
/* ============================================================================================== */

// New approach using the theme system
const NewComponent: React.FC = () => {
  const {theme} = useTheme();
  
  const styles = useThemedStyles((theme) => ({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background, // Theme-aware
      padding: theme.spacing.lg, // Consistent spacing
    },
    card: {
      backgroundColor: theme.colors.surface, // Theme-aware
      padding: theme.spacing.xl,
      borderRadius: theme.layout.borderRadius.md,
      marginBottom: theme.spacing.xl,
      shadowColor: theme.colors.shadow, // Theme-aware shadow
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    balance: {
      fontSize: theme.typography.xxl,
      fontWeight: '700',
      color: theme.colors.primary, // Theme-aware brand color
    },
  }));

  return (
    <ThemedView variant="container">
      <ThemedText variant="heading1" style={{marginBottom: 8}}>
        Welcome to Assetify
      </ThemedText>
      <ThemedText variant="body" color="textSecondary" style={{marginBottom: 24}}>
        Manage your crypto assets
      </ThemedText>
      
      <ThemedView style={styles.card} elevation={2}>
        <ThemedText variant="caption" color="textTertiary" style={{marginBottom: 8}}>
          Portfolio Balance
        </ThemedText>
        <Text style={styles.balance}>$12,345.67</Text>
      </ThemedView>

      <ThemedButton variant="primary" size="large">
        View Portfolio
      </ThemedButton>
    </ThemedView>
  );
};

/* ============================================================================================== */
/*                                    ALTERNATIVE APPROACH                                        */
/* ============================================================================================== */

// Alternative: Gradual migration using themed components with custom styles
const HybridComponent: React.FC = () => {
  const styles = useThemedStyles((theme) => ({
    customCard: {
      backgroundColor: theme.colors.surface,
      padding: theme.spacing.xl,
      borderRadius: theme.layout.borderRadius.lg,
      marginVertical: theme.spacing.lg,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    specialText: {
      fontSize: theme.typography.lg,
      fontWeight: '600',
      color: theme.colors.accent,
      textAlign: 'center',
    },
  }));

  return (
    <ThemedView variant="container">
      {/* Use themed components for standard elements */}
      <ThemedText variant="heading2">Hybrid Approach</ThemedText>
      
      {/* Mix themed components with custom styles when needed */}
      <ThemedView style={styles.customCard}>
        <ThemedText variant="body">Standard themed text</ThemedText>
        <Text style={styles.specialText}>Custom styled text</Text>
      </ThemedView>

      {/* Themed buttons work great out of the box */}
      <ThemedButton variant="outline" size="medium">
        Hybrid Button
      </ThemedButton>
    </ThemedView>
  );
};

/* ============================================================================================== */
/*                                      MIGRATION STEPS                                          */
/* ============================================================================================== */

/*
1. REPLACE HARDCODED COLORS:
   - '#FFFFFF' → theme.colors.background
   - '#000000' → theme.colors.text
   - '#666666' → theme.colors.textSecondary
   - Brand colors → theme.colors.primary/secondary

2. USE THEME SPACING:
   - padding: 16 → padding: theme.spacing.lg
   - margin: 8 → margin: theme.spacing.sm

3. USE THEME TYPOGRAPHY:
   - fontSize: 24 → fontSize: theme.typography.xl
   - fontSize: 16 → fontSize: theme.typography.sm

4. REPLACE COMPONENTS GRADUALLY:
   - View → ThemedView (with variants)
   - Text → ThemedText (with variants)
   - TouchableOpacity → ThemedButton (with variants)

5. USE THEMED STYLES HOOK:
   - StyleSheet.create → useThemedStyles

6. LEVERAGE THEME UTILITIES:
   - Shadow colors → theme.colors.shadow
   - Border radius → theme.layout.borderRadius.*
   - Elevation → ThemedView elevation prop
*/

export {OldComponent, NewComponent, HybridComponent};
