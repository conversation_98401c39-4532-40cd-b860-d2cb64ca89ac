import {useMemo} from 'react';
import {StyleSheet, TextStyle, ViewStyle, ImageStyle} from 'react-native';
import {useTheme} from '@/contexts/ThemeContext';
import {Theme, ThemeColors} from '@/styles/theme';

/* ============================================================================================== */
/*                                              TYPES                                             */
/* ============================================================================================== */

type NamedStyles<T> = {[P in keyof T]: ViewStyle | TextStyle | ImageStyle};

type StylesFactory<T extends NamedStyles<T>> = (theme: Theme) => T;

type ThemedStylesHook<T extends NamedStyles<T>> = () => T;

/* ============================================================================================== */
/*                                              HOOKS                                             */
/* ============================================================================================== */

/**
 * Hook for creating themed styles with automatic memoization
 * @param stylesFactory Function that takes theme and returns styles
 * @returns Memoized styles object
 */
export const useThemedStyles = <T extends NamedStyles<T>>(
  stylesFactory: StylesFactory<T>,
): T => {
  const {theme} = useTheme();

  return useMemo(() => {
    const styles = stylesFactory(theme);
    return StyleSheet.create(styles);
  }, [theme, stylesFactory]);
};

/**
 * Hook for accessing theme colors directly
 * @returns Current theme colors
 */
export const useThemeColors = (): ThemeColors => {
  const {theme} = useTheme();
  return theme.colors;
};

/**
 * Hook for creating a themed styles factory
 * This is useful when you want to create reusable style factories
 * @param stylesFactory Function that takes theme and returns styles
 * @returns Hook that returns themed styles
 */
export const createThemedStyles = <T extends NamedStyles<T>>(
  stylesFactory: StylesFactory<T>,
): ThemedStylesHook<T> => {
  return () => useThemedStyles(stylesFactory);
};

/* ============================================================================================== */
/*                                            UTILITIES                                           */
/* ============================================================================================== */

/**
 * Utility function to create conditional styles based on theme mode
 * @param lightStyle Style for light mode
 * @param darkStyle Style for dark mode
 * @returns Function that takes theme and returns appropriate style
 */
export const createConditionalStyle = <T extends ViewStyle | TextStyle | ImageStyle>(
  lightStyle: T,
  darkStyle: T,
) => {
  return (theme: Theme): T => {
    return theme.mode === 'dark' ? darkStyle : lightStyle;
  };
};

/**
 * Utility function to create styles with theme-aware colors
 * @param styleFactory Function that takes colors and returns style
 * @returns Function that takes theme and returns style with colors
 */
export const createColoredStyle = <T extends ViewStyle | TextStyle | ImageStyle>(
  styleFactory: (colors: ThemeColors) => T,
) => {
  return (theme: Theme): T => {
    return styleFactory(theme.colors);
  };
};

/**
 * Utility function to get color with opacity
 * @param color Base color
 * @param opacity Opacity value (0-1)
 * @returns Color with opacity
 */
export const withOpacity = (color: string, opacity: number): string => {
  // Handle hex colors
  if (color.startsWith('#')) {
    const hex = color.slice(1);
    const alpha = Math.round(opacity * 255).toString(16).padStart(2, '0');
    return `#${hex}${alpha}`;
  }
  
  // Handle rgba colors
  if (color.startsWith('rgba')) {
    return color.replace(/[\d.]+\)$/g, `${opacity})`);
  }
  
  // Handle rgb colors
  if (color.startsWith('rgb')) {
    return color.replace('rgb', 'rgba').replace(')', `, ${opacity})`);
  }
  
  return color;
};

/**
 * Utility function to create shadow styles based on theme
 * @param elevation Shadow elevation (0-24)
 * @returns Function that takes theme and returns shadow style
 */
export const createShadowStyle = (elevation: number) => {
  return (theme: Theme): ViewStyle => {
    if (elevation === 0) {
      return {};
    }

    const shadowOpacity = theme.mode === 'dark' ? 0.3 : 0.1;
    const shadowRadius = elevation * 0.8;
    const shadowOffset = {
      width: 0,
      height: elevation * 0.5,
    };

    return {
      shadowColor: theme.colors.shadow,
      shadowOffset,
      shadowOpacity,
      shadowRadius,
      elevation, // Android
    };
  };
};

/* ============================================================================================== */
/*                                        COMMON STYLES                                          */
/* ============================================================================================== */

/**
 * Common themed styles that can be reused across components
 */
export const commonThemedStyles = {
  container: (theme: Theme): ViewStyle => ({
    flex: 1,
    backgroundColor: theme.colors.background,
  }),

  surface: (theme: Theme): ViewStyle => ({
    backgroundColor: theme.colors.surface,
    borderRadius: theme.layout.borderRadius.md,
    ...createShadowStyle(2)(theme),
  }),

  card: (theme: Theme): ViewStyle => ({
    backgroundColor: theme.colors.surface,
    borderRadius: theme.layout.borderRadius.md,
    padding: theme.spacing.lg,
    marginVertical: theme.spacing.sm,
    ...createShadowStyle(1)(theme),
  }),

  divider: (theme: Theme): ViewStyle => ({
    height: 1,
    backgroundColor: theme.colors.border,
    marginVertical: theme.spacing.sm,
  }),

  button: (theme: Theme): ViewStyle => ({
    backgroundColor: theme.colors.primary,
    borderRadius: theme.layout.borderRadius.sm,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
  }),

  buttonText: (theme: Theme): TextStyle => ({
    color: theme.colors.textInverse,
    fontSize: theme.typography.sm,
    fontWeight: '600',
  }),

  input: (theme: Theme): ViewStyle => ({
    backgroundColor: theme.colors.surface,
    borderColor: theme.colors.border,
    borderWidth: 1,
    borderRadius: theme.layout.borderRadius.sm,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    fontSize: theme.typography.sm,
    color: theme.colors.text,
  }),
};

/* ============================================================================================== */
/*                                             EXPORTS                                            */
/* ============================================================================================== */

export type {NamedStyles, StylesFactory, ThemedStylesHook};
