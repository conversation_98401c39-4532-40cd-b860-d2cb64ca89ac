import {DefaultTheme, DarkTheme, Theme as NavigationTheme} from '@react-navigation/native';
import {lightThemeColors, darkThemeColors, ThemeColors} from '@/styles/theme';

/* ============================================================================================== */
/*                                              TYPES                                             */
/* ============================================================================================== */

interface CustomNavigationTheme extends NavigationTheme {
  colors: NavigationTheme['colors'] & {
    surface: string;
    surfaceSecondary: string;
    textSecondary: string;
    textTertiary: string;
    success: string;
    warning: string;
    error: string;
    info: string;
    accent: string;
    disabled: string;
    placeholder: string;
    shadow: string;
    overlay: string;
  };
}

/* ============================================================================================== */
/*                                            UTILITIES                                           */
/* ============================================================================================== */

const createNavigationTheme = (
  baseTheme: NavigationTheme,
  themeColors: ThemeColors,
): CustomNavigationTheme => {
  return {
    ...baseTheme,
    colors: {
      ...baseTheme.colors,
      primary: themeColors.primary,
      background: themeColors.background,
      card: themeColors.surface,
      text: themeColors.text,
      border: themeColors.border,
      notification: themeColors.accent,
      
      // Custom colors
      surface: themeColors.surface,
      surfaceSecondary: themeColors.surfaceSecondary,
      textSecondary: themeColors.textSecondary,
      textTertiary: themeColors.textTertiary,
      success: themeColors.success,
      warning: themeColors.warning,
      error: themeColors.error,
      info: themeColors.info,
      accent: themeColors.accent,
      disabled: themeColors.disabled,
      placeholder: themeColors.placeholder,
      shadow: themeColors.shadow,
      overlay: themeColors.overlay,
    },
  };
};

/* ============================================================================================== */
/*                                        NAVIGATION THEMES                                       */
/* ============================================================================================== */

export const lightNavigationTheme: CustomNavigationTheme = createNavigationTheme(
  DefaultTheme,
  lightThemeColors,
);

export const darkNavigationTheme: CustomNavigationTheme = createNavigationTheme(
  DarkTheme,
  darkThemeColors,
);

/* ============================================================================================== */
/*                                        THEME UTILITIES                                         */
/* ============================================================================================== */

export const getNavigationTheme = (isDark: boolean): CustomNavigationTheme => {
  return isDark ? darkNavigationTheme : lightNavigationTheme;
};

/* ============================================================================================== */
/*                                        HEADER STYLES                                           */
/* ============================================================================================== */

export const createThemedHeaderStyle = (themeColors: ThemeColors) => ({
  headerStyle: {
    backgroundColor: themeColors.surface,
    shadowColor: themeColors.shadow,
    elevation: 1,
  },
  headerTintColor: themeColors.text,
  headerTitleStyle: {
    fontWeight: '600' as const,
    color: themeColors.text,
  },
  headerBackTitleStyle: {
    color: themeColors.textSecondary,
  },
});

export const createThemedTabBarStyle = (themeColors: ThemeColors) => ({
  tabBarStyle: {
    backgroundColor: themeColors.surface,
    borderTopColor: themeColors.border,
    shadowColor: themeColors.shadow,
    elevation: 8,
  },
  tabBarActiveTintColor: themeColors.primary,
  tabBarInactiveTintColor: themeColors.textSecondary,
  tabBarLabelStyle: {
    fontSize: 12,
    fontWeight: '500' as const,
  },
});

/* ============================================================================================== */
/*                                             EXPORTS                                            */
/* ============================================================================================== */

export type {CustomNavigationTheme};
