import React, {useState} from 'react';
import {ScrollView, Switch, Alert} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';

import {
  ThemedView,
  ThemedText,
  ThemedButton,
  useTheme,
  useThemedStyles,
} from '@/components/themed';
import {ThemeMode} from '@/styles/theme';

/* ============================================================================================== */
/*                                              TYPES                                             */
/* ============================================================================================== */

interface ThemeSelectorProps {
  currentMode: ThemeMode;
  onModeChange: (mode: ThemeMode) => void;
}

/* ============================================================================================== */
/*                                            COMPONENTS                                          */
/* ============================================================================================== */

const ThemeSelector: React.FC<ThemeSelectorProps> = ({currentMode, onModeChange}) => {
  const styles = useThemedStyles((theme) => ({
    container: {
      padding: theme.spacing.lg,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.layout.borderRadius.md,
      marginBottom: theme.spacing.lg,
    },
    row: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginVertical: theme.spacing.sm,
    },
    modeButton: {
      flex: 1,
      marginHorizontal: theme.spacing.sm,
    },
  }));

  const modes: {mode: ThemeMode; label: string}[] = [
    {mode: 'light', label: 'Light'},
    {mode: 'dark', label: 'Dark'},
    {mode: 'system', label: 'System'},
  ];

  return (
    <ThemedView style={styles.container} elevation={2}>
      <ThemedText variant="heading3" style={{marginBottom: 16}}>
        Theme Settings
      </ThemedText>
      
      <ThemedView style={styles.row}>
        {modes.map(({mode, label}) => (
          <ThemedButton
            key={mode}
            variant={currentMode === mode ? 'primary' : 'outline'}
            size="small"
            style={styles.modeButton}
            onPress={() => onModeChange(mode)}
          >
            {label}
          </ThemedButton>
        ))}
      </ThemedView>
    </ThemedView>
  );
};

const ComponentShowcase: React.FC = () => {
  const [switchValue, setSwitchValue] = useState(false);
  const {theme} = useTheme();

  const styles = useThemedStyles((theme) => ({
    section: {
      padding: theme.spacing.lg,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.layout.borderRadius.md,
      marginBottom: theme.spacing.lg,
    },
    row: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginVertical: theme.spacing.sm,
    },
    buttonRow: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: theme.spacing.sm,
      marginVertical: theme.spacing.sm,
    },
    colorBox: {
      width: 40,
      height: 40,
      borderRadius: theme.layout.borderRadius.sm,
      marginRight: theme.spacing.sm,
    },
    colorRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: theme.spacing.sm,
    },
  }));

  const showAlert = (title: string): void => {
    Alert.alert('Demo Action', `${title} button pressed!`);
  };

  return (
    <>
      {/* Typography Section */}
      <ThemedView style={styles.section} elevation={1}>
        <ThemedText variant="heading3" style={{marginBottom: 16}}>
          Typography
        </ThemedText>
        <ThemedText variant="heading1">Heading 1</ThemedText>
        <ThemedText variant="heading2">Heading 2</ThemedText>
        <ThemedText variant="heading3">Heading 3</ThemedText>
        <ThemedText variant="body">Body text with normal weight</ThemedText>
        <ThemedText variant="bodySmall">Small body text</ThemedText>
        <ThemedText variant="caption">Caption text</ThemedText>
        <ThemedText variant="label">Label text</ThemedText>
      </ThemedView>

      {/* Buttons Section */}
      <ThemedView style={styles.section} elevation={1}>
        <ThemedText variant="heading3" style={{marginBottom: 16}}>
          Buttons
        </ThemedText>
        
        <ThemedView style={styles.buttonRow}>
          <ThemedButton variant="primary" onPress={() => showAlert('Primary')}>
            Primary
          </ThemedButton>
          <ThemedButton variant="secondary" onPress={() => showAlert('Secondary')}>
            Secondary
          </ThemedButton>
        </ThemedView>

        <ThemedView style={styles.buttonRow}>
          <ThemedButton variant="outline" onPress={() => showAlert('Outline')}>
            Outline
          </ThemedButton>
          <ThemedButton variant="ghost" onPress={() => showAlert('Ghost')}>
            Ghost
          </ThemedButton>
          <ThemedButton variant="danger" onPress={() => showAlert('Danger')}>
            Danger
          </ThemedButton>
        </ThemedView>

        <ThemedView style={styles.buttonRow}>
          <ThemedButton size="small" onPress={() => showAlert('Small')}>
            Small
          </ThemedButton>
          <ThemedButton size="medium" onPress={() => showAlert('Medium')}>
            Medium
          </ThemedButton>
          <ThemedButton size="large" onPress={() => showAlert('Large')}>
            Large
          </ThemedButton>
        </ThemedView>

        <ThemedView style={styles.buttonRow}>
          <ThemedButton disabled onPress={() => showAlert('Disabled')}>
            Disabled
          </ThemedButton>
          <ThemedButton loading onPress={() => showAlert('Loading')}>
            Loading
          </ThemedButton>
        </ThemedView>
      </ThemedView>

      {/* Colors Section */}
      <ThemedView style={styles.section} elevation={1}>
        <ThemedText variant="heading3" style={{marginBottom: 16}}>
          Theme Colors
        </ThemedText>
        
        {Object.entries(theme.colors).map(([key, color]) => (
          <ThemedView key={key} style={styles.colorRow}>
            <ThemedView 
              style={[styles.colorBox, {backgroundColor: color}]} 
              borderColor="border"
            />
            <ThemedView style={{flex: 1}}>
              <ThemedText variant="body">{key}</ThemedText>
              <ThemedText variant="caption" color="textSecondary">
                {color}
              </ThemedText>
            </ThemedView>
          </ThemedView>
        ))}
      </ThemedView>

      {/* Interactive Elements */}
      <ThemedView style={styles.section} elevation={1}>
        <ThemedText variant="heading3" style={{marginBottom: 16}}>
          Interactive Elements
        </ThemedText>
        
        <ThemedView style={styles.row}>
          <ThemedText variant="body">Switch Component</ThemedText>
          <Switch
            value={switchValue}
            onValueChange={setSwitchValue}
            trackColor={{
              false: theme.colors.border,
              true: theme.colors.primary,
            }}
            thumbColor={switchValue ? theme.colors.surface : theme.colors.disabled}
          />
        </ThemedView>
      </ThemedView>
    </>
  );
};

/* ============================================================================================== */
/*                                          MAIN SCREEN                                          */
/* ============================================================================================== */

const ThemeDemoScreen: React.FC = () => {
  const {themeMode, setThemeMode, colorScheme, isDark} = useTheme();

  const styles = useThemedStyles((theme) => ({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContent: {
      padding: theme.spacing.lg,
    },
    header: {
      alignItems: 'center',
      marginBottom: theme.spacing.xl,
    },
    statusText: {
      textAlign: 'center',
      marginTop: theme.spacing.sm,
    },
  }));

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <ThemedView style={styles.header}>
          <ThemedText variant="heading1">Theme Demo</ThemedText>
          <ThemedText variant="body" color="textSecondary" style={styles.statusText}>
            Current: {colorScheme} mode {isDark ? '🌙' : '☀️'}
          </ThemedText>
          <ThemedText variant="caption" color="textTertiary" style={styles.statusText}>
            Setting: {themeMode}
          </ThemedText>
        </ThemedView>

        <ThemeSelector currentMode={themeMode} onModeChange={setThemeMode} />
        <ComponentShowcase />
      </ScrollView>
    </SafeAreaView>
  );
};

export default ThemeDemoScreen;
