import analytics from '@react-native-firebase/analytics';
import {useFocusEffect} from '@react-navigation/native';
import i18next from 'i18next';
import React, {memo, useCallback, useEffect, useState} from 'react';
import {initReactI18next, useTranslation} from 'react-i18next';
import {Dimensions, RefreshControl, ScrollView, StyleSheet, View} from 'react-native';

import {ChainToTokens} from '@/constants/Chains';
import GlobalStyles from '@/constants/GlobalStyles';
import {languageResources} from '@/constants/i18next';
import {useDebounce} from '@/hooks';
import {useBottomSheet} from '@/hooks/bottomSheet';
import {useAppDispatch, useAuth, useCommon} from '@/hooks/redux';
import {useTheme} from '@/contexts/ThemeContext';
import {useThemedStyles} from '@/hooks/useThemedStyles';
import {navigate} from '@/navigation/utils/navigation';
import {
  setAvalancheFixed,
  setIsAuthenticated,
  setShowAuthScreen,
  setUser,
  setUserAddresses,
} from '@/storage/actions/authActions';
import CarouselParallax from '@components/CarouselParallax';
import LoadingHandler from '@components/LoadingHandler';
import SafeAreaInset from '@components/SafeAreaInset';
import SendReceiveButtons from '@components/SendReceiveButtons/SendReceiveButtons';
import theme from '@styles/theme';
import AssetList from './components/AssetList/AssetList';
import TopNavigation from './components/TopNavigation/TopNavigation';
import WalletBalance from './components/WalletBalance/WalletBalance';
import {useSecondaryAssets} from './hooks/useSecondaryAssets';
import {useWalletData} from './hooks/useWalletData';
import {
  checkNotifSubscription,
  checkSolanaWallet,
  checkWallets,
} from './walletScreenUtils';

const WalletScreen = () => {
  const {t} = useTranslation();
  const dispatch = useAppDispatch();
  const {open: openSheet, close: closeSheet} = useBottomSheet('wallet');
  const {theme} = useTheme();

  const {
    assets: authAssets,
    calculatedPrices: authCalculatedPrices,
    stableCoins: authStableCoins,
    tokenPrices: authTokenPrices,
    walletBalance: authWalletBalance,
    userAddresses,
    user,
    avalancheFixed,
  } = useAuth();

  const {currency, language} = useCommon();

  const walletData = useWalletData(
    userAddresses[0]?.address,
    currency,
    authAssets,
    authCalculatedPrices,
    authStableCoins,
    authTokenPrices,
    authWalletBalance,
  );

  const secondaryAssets = useSecondaryAssets(user, userAddresses);

  const debouncedLoading = useDebounce(
    walletData.loading || secondaryAssets.loading,
    500,
  );

  const [blur, setBlur] = useState(false);

  const themedStyles = useThemedStyles((theme) => ({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollView: {
      backgroundColor: theme.colors.background,
    },
    balance: {
      backgroundColor: theme.colors.surface,
      borderRadius: 10,
      overflow: 'hidden',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      alignSelf: 'center',
      paddingVertical: 6,
      paddingHorizontal: 12,
      display: 'flex',
      width: '90%',
      elevation: 4,
      marginBottom: 4,
      shadowColor: theme.colors.shadow,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
    },
    assetsContainer: {
      flexDirection: 'column',
      alignItems: 'center',
      display: 'flex',
      alignSelf: 'center',
      width: '90%',
      paddingBottom: theme.spacing.lg,
    },
    carouselContainer: {
      marginBottom: 18,
      width: '100%',
      paddingHorizontal: 0,
      alignItems: 'center',
    },
  }));

  const handleMenuPress = useCallback(() => {
    analytics().logEvent('Home_infoButton_tap');
    navigate('Info');
  }, []);

  const handleSettingsPress = useCallback(() => {
    analytics().logEvent('Home_settingsButton_tap');
    navigate('Settings');
  }, []);

  const handleNotificationsPress = useCallback(() => {
    navigate('Notifications');
  }, []);

  const handleSendPress = useCallback(() => {
    analytics().logEvent('Home_sendButton_tap');
    navigate('BottomTabs', {
      screen: 'Wallet',
      params: {
        screen: 'SendAssetInput',
        params: {
          assets: walletData.assets,
          tokenPrices: walletData.tokenPrices,
          calculatedPrices: walletData.calculatedPrices,
          stableCoins: walletData.stableCoins,
        },
      },
    });
  }, [
    walletData.assets,
    walletData.tokenPrices,
    walletData.calculatedPrices,
    walletData.stableCoins,
  ]);

  const handleReceivePress = useCallback(() => {
    analytics().logEvent('Home_receiveButton_tap');

    navigate('BottomTabs', {
      screen: 'Wallet',
      params: {
        screen: 'ReceiveAsset',
        params: {
          assets: walletData.assets,
          tokenPrices: walletData.tokenPrices,
        },
      },
    });
  }, [walletData.assets, walletData.tokenPrices]);

  const handleAssetPress = useCallback(
    (asset: any) => {
      console.log('asset', asset);

      const index = user.wallet.findIndex((w) => w && w.blockchain === asset.blockchain);

      const addressFix =
        asset.blockchain === 'kaspa'
          ? userAddresses[index]?.addresses[0]
          : userAddresses[index]?.address.includes('bitcoincash:')
          ? userAddresses[index]?.address.split('bitcoincash:')[1]
          : userAddresses[index]?.address;

      console.log('INDEX >>>>', index);
      console.log('addresses', addressFix);
      if (!addressFix) return;

      navigate('CurrencySpecific', {
        asset,
        stableCoin: '',
        stableCoinIndex: 0,
        stableCoinAmount: '0.00',
        address: addressFix,
        calculatedPrices: walletData.calculatedPrices[index] || [],
        tokenPrices: walletData.tokenPrices[index] || [],
      });
    },
    [walletData.assets, walletData.tokenPrices, walletData.calculatedPrices],
  );

  const handleStableCoinPress = useCallback(
    (asset: any, stableCoin: string) => {
      const index = user.wallet.findIndex((w) => w && w.blockchain === asset.title);
      const stableCoinIndex =
        ChainToTokens[asset.title]?.findIndex((t) => t.tokenSymbol === stableCoin) + 1;

      if (!stableCoinIndex) return;

      const amount = walletData.stableCoins[index]?.[stableCoinIndex - 1] ?? '0';
      console.log('amount', amount);
      if (
        !amount ||
        !walletData.calculatedPrices[index] ||
        !walletData.tokenPrices[index]
      ) {
        return;
      }

      navigate('CurrencySpecific', {
        asset,
        stableCoin,
        stableCoinIndex,
        stableCoinAmount: amount,
        address: userAddresses[index],
        calculatedPrices: walletData.calculatedPrices[index] || [],
        tokenPrices: walletData.tokenPrices[index] || [],
      });
    },
    [walletData.assets, walletData.tokenPrices, walletData.calculatedPrices],
  );

  const handleBuyPress = useCallback(() => {
    analytics().logEvent('Home_buyButton_tap');
    openSheet({
      type: 'wallet',
      assets: walletData.assets,
      calculatedPrices: walletData.calculatedPrices,
      tokenPrices: walletData.tokenPrices,
      stableCoins: walletData.stableCoins,
      handleAssetPress: (asset) => {
        handleRampAssetPress(asset);
        closeSheet();
      },
      handleStableCoinPress: (asset, stableCoin) => {
        handleRampStableCoinPress(asset, stableCoin);
        closeSheet();
      },
      blur,
      setBlur,
      currency,
    });
  }, [
    walletData.assets,
    walletData.tokenPrices,
    walletData.calculatedPrices,
    walletData.stableCoins,
  ]);

  const handleRampAssetPress = useCallback(
    (asset: any) => {
      const index = user.wallet.findIndex((w) => w && w.blockchain === asset.blockchain);
      navigate('RampDetails', {
        currency: asset.title,
        currencySymbol: asset.tokenSymbol,
        tokenPrice: walletData.tokenPrices[index]?.[0] || [],
        addressToUse: userAddresses[index]?.address,
      });
    },
    [walletData.tokenPrices, userAddresses, user.wallet],
  );

  const handleRampStableCoinPress = useCallback(
    (asset: any, stableCoin: string) => {
      const index = user.wallet.findIndex((w) => w && w.blockchain === asset.title);
      const normalizedStableCoin = stableCoin === 'USDT20' ? 'USDT' : stableCoin;
      const stableCoinIndex =
        ChainToTokens[asset.title]?.findIndex(
          (t) => t.tokenSymbol === normalizedStableCoin,
        ) + 1;

      if (!stableCoinIndex) return;

      navigate('RampDetails', {
        currency: normalizedStableCoin,
        currencySymbol: normalizedStableCoin,
        tokenPrice: walletData.tokenPrices[index]?.[stableCoinIndex],
        addressToUse: userAddresses[index]?.address,
        blockchainSymbol: asset.tokenSymbol,
      });
    },
    [walletData.tokenPrices, userAddresses, user.wallet],
  );

  useFocusEffect(
    useCallback(() => {
      // setSentryContext(userAddresses[0]?.address);

      const initializeWallet = async () => {
        if (user !== null && !avalancheFixed) {
          const check = await checkWallets(user.wallet, userAddresses);
          if (check !== null) {
            dispatch(setUser({...user, wallet: check.wallets}));
            dispatch(setUserAddresses(check.addresses));
            dispatch(setAvalancheFixed());
            walletData.refreshData();
          } else if (!avalancheFixed) {
            dispatch(setAvalancheFixed());
          }
        }

        analytics().logEvent('Home_screen_open');
        checkNotifSubscription(userAddresses[0]?.address);
        walletData.refreshData();

        i18next.use(initReactI18next).init({
          compatibilityJSON: 'v3',
          lng: language,
          fallbackLng: 'en',
          resources: languageResources,
        });

        const availableAssets = await secondaryAssets.getAvailableSecondaryAssets();
        console.log('availableAssets', availableAssets);
        if (availableAssets.length > 0) {
          const success = await secondaryAssets.createSecondaryWallets(availableAssets);
          if (success) {
            walletData.refreshData();
          }
        }

        const result = await checkSolanaWallet(user.wallet, userAddresses);
        if (result !== null) {
          dispatch(setUserAddresses(result.addresses));
          dispatch(setUser({...user, wallet: result.wallets}));
        }
      };

      initializeWallet();
    }, []),
  );

  useEffect(() => {
    walletData.setLoading(true);
  }, [currency]);

  if (debouncedLoading) {
    return (
      <View
        style={[themedStyles.container, {justifyContent: 'center', alignItems: 'center'}]}
      >
        <LoadingHandler />
      </View>
    );
  }

  return (
    <View style={themedStyles.container}>
      <SafeAreaInset type="top" />

      <TopNavigation
        handleMenu={handleMenuPress}
        handleNotifications={handleNotificationsPress}
        handleSettings={handleSettingsPress}
        title={t('wallet.title')}
      />
      <ScrollView
        style={themedStyles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={walletData.refreshing}
            onRefresh={walletData.onRefresh}
            tintColor={theme.colors.primary}
            colors={[theme.colors.primary]}
          />
        }
      >
        <View style={themedStyles.carouselContainer}>
          <CarouselParallax
            images={[
              {
                link: 'Subscribe',
                title: 'Need a Loan?',
                subtitle: 'Get a crypto-backed fiat loan in 10 minutes',
              },
              {
                link: 'Subscribe',
                title: 'Your Crypto, Simplified',
                subtitle: 'Secure, flexible, and tailored to you',
              },
            ]}
            height={200}
          />
        </View>

        <View style={themedStyles.balance}>
          <WalletBalance balance={walletData.balance} blur={blur} currency={currency} />

          <SendReceiveButtons
            handleSendPress={handleSendPress}
            handleReceivePress={handleReceivePress}
            handleBuyPress={handleBuyPress}
            buyButtonTitle="Buy / Sell"
          />
        </View>

        <View style={themedStyles.assetsContainer}>
          <AssetList
            assets={walletData.assets}
            blur={blur}
            setBlur={setBlur}
            tokenPrices={walletData.tokenPrices}
            handleAssetPress={handleAssetPress}
            handleStableCoinPress={handleStableCoinPress}
            currency={currency}
          />
        </View>
      </ScrollView>
    </View>
  );
};

export default memo(WalletScreen);

// Styles moved to themedStyles hook above
