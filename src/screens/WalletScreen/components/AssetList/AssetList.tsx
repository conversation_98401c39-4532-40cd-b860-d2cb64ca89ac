import {useTranslation} from 'react-i18next';
import {TouchableOpacity, View} from 'react-native';
import FixedText from '../../../../components/FixedText/FixedText';
import WalletAsset from './components/WalletAsset/WalletAsset';
import {useSelector} from 'react-redux';
import {useMemo} from 'react';
import {AuthAsset, AuthTokenPrices} from '@/types/authTypes';
import {useThemedStyles} from '@/hooks/useThemedStyles';
import GlobalStyles from '@/constants/GlobalStyles';

type AssetListProps = {
  assets: Array<AuthAsset>;
  // calculatedPrices: AuthCalculatedPrices;
  // stableCoins: AuthStableCoins;
  tokenPrices: AuthTokenPrices;
  handleAssetPress: (asset: AuthAsset) => void;
  handleStableCoinPress: (asset: AuthAsset, stableCoin: string) => void;
  blur: boolean;
  setBlur: (value: boolean) => void;
  currency: string;
};

const AssetList = ({
  assets,
  // calculatedPrices,
  tokenPrices,
  // stableCoins,
  handleAssetPress,
  handleStableCoinPress,
  blur,
  setBlur,
  currency,
}: AssetListProps) => {
  const {t} = useTranslation();

  const styles = useThemedStyles((theme) => ({
    titleText: {
      fontSize: 16,
      lineHeight: 25,
      fontWeight: '500',
      fontFamily: GlobalStyles.fonts.sfPro,
      color: theme.colors.text,
      fontStyle: 'normal',
    },
    titleContainer: {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 10,
    },
    seeAllText: {
      fontSize: 14,
      lineHeight: 21,
      fontWeight: '400',
      fontFamily: GlobalStyles.fonts.sfPro,
      color: theme.colors.textSecondary,
      fontStyle: 'normal',
    },
    listContainer: {
      flex: 1,
      flexDirection: 'column',
      width: '100%',
    },
    container: {
      marginTop: 10,
      marginBottom: -10,
      display: 'flex',
      width: '100%',
      height: '100%',
    },
  }));

  // prettier-ignore
  const calculatedPrices = useSelector((state: any) => state.auth.calculatedPrices)
  // prettier-ignore
  const memoizedCalculatedPrices = useMemo(() => calculatedPrices, [calculatedPrices])

  const stableCoins = useSelector((state: any) => state.auth.stableCoins);
  const memoizedStableCoins = useMemo(() => stableCoins, [stableCoins]);

  return (
    <View style={styles.container}>
      <View style={styles.titleContainer}>
        <FixedText style={styles.titleText}>{t('wallet.assets')}</FixedText>
        <TouchableOpacity onPress={() => setBlur(!blur)}>
          <FixedText style={styles.seeAllText}>
            {blur ? t('wallet.showBalance') : t('wallet.hideBalance')}
          </FixedText>
        </TouchableOpacity>
      </View>
      <View style={styles.listContainer}>
        {assets.map((item, index) => {
          if (item.tokenSymbol === 'N/A') return null;
          return (
            <View key={`${item}-${index}`}>
              <WalletAsset
                key={index}
                index={index}
                asset={item}
                calculatedPrices={memoizedCalculatedPrices[index]}
                tokenPrices={tokenPrices[index]}
                stableCoins={memoizedStableCoins[index]}
                handleAssetPress={handleAssetPress}
                handleStableCoinPress={handleStableCoinPress}
                blur={blur}
                currency={currency}
              />
            </View>
          );
        })}
      </View>
    </View>
  );
};

export default AssetList;
