// Styles moved to WalletAsset component using useThemedStyles hook
  assetContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  horizontalLine: {
    width: '100%',
    height: 2,
    backgroundColor: GlobalStyles.gray.gray500,
    marginVertical: 10,
    left: -6,
  },
  shortLine: {
    width: 30,
    height: 2,
    backgroundColor: GlobalStyles.gray.gray500,
    left: 20,
    top: 46,
  },
  verticalLine: {
    width: 2,
    position: 'absolute',
    backgroundColor: GlobalStyles.gray.gray500,
    left: 18,
    top: -3,
    zIndex: -1,
  },
  leftContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  tokenLogoContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: GlobalStyles.primary.primary100,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    flexDirection: 'column',
    gap: -50,
  },
  stableCoinLogoContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: GlobalStyles.primary.primary100,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 20,
  },
  tokenLogo: {
    width: 40,
    height: 40,
  },
  tokenInfoContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  tokenName: {
    fontSize: 15.5,
    lineHeight: 22,
    fontWeight: '500',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.base.black,
    fontStyle: 'normal',
  },
  tokenPrice: {
    fontSize: 14,
    lineHeight: 21,
    fontWeight: '400',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.base.black,
    fontStyle: 'normal',
  },
  tokenAmount: {
    fontSize: 14,
    lineHeight: 21,
    fontWeight: 'bold',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.base.black,
    fontStyle: 'normal',
    marginLeft: 5,
    marginRight: 5,
  },
  rightContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-end',
    marginRight: -5,
  },
  tokenPriceUp: {
    fontSize: 14,
    lineHeight: 21,
    fontWeight: '400',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.success.success700,
    fontStyle: 'normal',
  },
  tokenPriceDown: {
    fontSize: 14,
    lineHeight: 21,
    fontWeight: '400',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.error.error500,
    fontStyle: 'normal',
  },
  calculatedPrice: {
    fontSize: 14,
    lineHeight: 21,
    fontWeight: 'bold',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.gray.gray900,
    fontStyle: 'normal',
    marginLeft: 5,
    marginRight: 5,
  },
});
