import {TouchableOpacity, View} from 'react-native';
import Logo from '../../../../assets/logo/Logo.svg';
import LogoWhite from '../../../../assets/logo/LogoWhite.svg';
import Notifications from '../../../../assets/nav/notifications.svg';
import Settings from '../../../../assets/nav/settings.svg';
import FixedText from '../../../../components/FixedText/FixedText';
import {useThemedStyles} from '@/hooks/useThemedStyles';
import {useTheme} from '@/contexts/ThemeContext';
import GlobalStyles from '@/constants/GlobalStyles';

const NotificationsSvg = () => <Notifications width={30} height={30} />;
const SettingsSvg = () => <Settings width={30} height={30} />;

type TopNavigationProps = {
  handleMenu: () => void;
  handleSettings: () => void;
  handleNotifications: () => void;
  title: string;
};

const TopNavigation = ({
  handleMenu,
  handleSettings,
  handleNotifications,
  title,
}: TopNavigationProps) => {
  const {isDark} = useTheme();

  const LogoSvg = () => {
    return isDark ? (
      <LogoWhite width={37} height={37} />
    ) : (
      <Logo width={37} height={37} />
    );
  };

  const styles = useThemedStyles((theme) => ({
    container: {
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'center',
      width: '100%',
      marginVertical: 18,
    },
    title: {
      color: theme.colors.text,
      textAlign: 'center',
      fontFamily: GlobalStyles.fonts.sfPro,
      fontSize: 18,
      fontStyle: 'normal',
      fontWeight: 'bold',
      lineHeight: 22,
    },
    iconContainerLeft: {
      display: 'flex',
      width: 40,
      height: 40,
      position: 'absolute',
      left: 21,
      gap: 10,
      flexShrink: 0,
    },
    iconContainerRight: {
      position: 'absolute',
      display: 'flex',
      flexDirection: 'row',
      width: 100,
      height: 30,
      justifyContent: 'flex-end',
      alignItems: 'center',
      gap: 10,
      right: 20,
    },
    hide: {
      display: 'none',
    },
    show: {
      display: 'flex',
      justifyContent: 'center',
    },
  }));

  return (
    <View style={styles.container}>
      <View style={styles.iconContainerLeft}>
        <TouchableOpacity onPress={handleMenu}>
          <LogoSvg />
        </TouchableOpacity>
      </View>
      <View style={title ? styles.show : styles.hide}>
        <FixedText style={styles.title}>{title}</FixedText>
      </View>
      <View style={styles.iconContainerRight}>
        <TouchableOpacity onPress={handleNotifications}>
          <NotificationsSvg />
        </TouchableOpacity>
        <TouchableOpacity onPress={handleSettings}>
          <SettingsSvg />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default TopNavigation;
