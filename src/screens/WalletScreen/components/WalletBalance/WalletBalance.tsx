import {useTranslation} from 'react-i18next';
import {Text, View} from 'react-native';

import BlurView from '@/components/BlurView';
import {useThemedStyles} from '@/hooks/useThemedStyles';
import GlobalStyles from '@/constants/GlobalStyles';

type WalletBalanceProps = {
  balance: string;
  blur: boolean;
  currency: string;
};

const WalletBalance = ({balance, blur, currency}: WalletBalanceProps) => {
  const {t} = useTranslation();

  const styles = useThemedStyles((theme) => ({
    topContainer: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      marginTop: 10,
    },
    balanceContainer: {
      alignItems: 'flex-start',
      justifyContent: 'center',
      flexDirection: 'row',
      marginRight: 3.5,
    },
    balanceText: {
      fontSize: 22,
      lineHeight: 25,
      fontWeight: '500',
      fontFamily: GlobalStyles.fonts.sfPro,
      textAlign: 'center',
      color: theme.colors.textSecondary,
      fontStyle: 'normal',
      marginLeft: -6,
    },
    balanceValue: {
      color: theme.colors.primary,
      textAlign: 'center',
      textAlignVertical: 'center',
      fontFamily: GlobalStyles.fonts.sfPro,
      fontSize: 39,
      fontWeight: '600',
      lineHeight: 60,
      marginRight: -6,
      marginLeft: 5,
    },
    balanceValueAfterDecimal: {
      color: theme.colors.primaryLight,
      textAlign: 'center',
      textAlignVertical: 'center',
      fontFamily: GlobalStyles.fonts.sfPro,
      fontSize: 20,
      fontWeight: '600',
      lineHeight: 50,
      marginRight: 5,
    },
    dollarSign: {
      fontSize: 18,
      lineHeight: 50,
      fontWeight: '600',
      fontFamily: GlobalStyles.fonts.sfPro,
      color: theme.colors.primary,
      fontStyle: 'normal',
      textAlign: 'center',
      textAlignVertical: 'center',
      marginLeft: 5,
    },
  }));

  let balanceBeforeDecimal = balance.split('.')[0];
  // Split the price before the decimal point into groups of 3 digits
  balanceBeforeDecimal = balanceBeforeDecimal.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  // Get the balance after the decimal point and slice it to 2 digits
  let balanceAfterDecimal = balance.split('.')[1]
    ? balance.split('.')[1].slice(0, 2)
    : '00';

  return (
    <View style={styles.topContainer}>
      <BlurView isBlurred={blur}>
        <Text style={styles.balanceText}>{t('balance')}</Text>
        <View style={styles.balanceContainer}>
          <Text style={styles.dollarSign}>{currency === 'EUR' ? '€' : '$'}</Text>
          <View style={styles.balanceContainer}>
            <Text style={styles.balanceValue}>{balanceBeforeDecimal} </Text>
            <Text style={styles.balanceValueAfterDecimal}> .{balanceAfterDecimal}</Text>
          </View>
        </View>
      </BlurView>
    </View>
  );
};

export default WalletBalance;
