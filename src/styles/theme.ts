import {Dimensions, PixelRatio} from 'react-native';

export const {width: SCREEN_WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');

const scale = SCREEN_WIDTH / 375;
const scaleHeight = SCREEN_HEIGHT / 812;

/* ============================================================================================== */
/*                                              TYPES                                             */
/* ============================================================================================== */

export type ThemeMode = 'light' | 'dark' | 'system';

export type ColorScheme = 'light' | 'dark';

export interface ThemeColors {
  // Background colors
  background: string;
  backgroundSecondary: string;
  backgroundTertiary: string;
  surface: string;
  surfaceSecondary: string;

  // Text colors
  text: string;
  textSecondary: string;
  textTertiary: string;
  textInverse: string;

  // Brand colors
  primary: string;
  primaryLight: string;
  primaryDark: string;
  secondary: string;
  secondaryLight: string;
  secondaryDark: string;

  // Status colors
  success: string;
  warning: string;
  error: string;
  info: string;

  // Interactive colors
  border: string;
  borderSecondary: string;
  borderFocus: string;
  shadow: string;
  overlay: string;

  // Special colors
  accent: string;
  disabled: string;
  placeholder: string;
}

export interface Theme {
  colors: ThemeColors;
  mode: ColorScheme;
  fonts: typeof fonts;
  typography: typeof typography;
  spacing: typeof spacing;
  layout: typeof layout;
}

/* ============================================================================================== */
/*                                              UTILS                                             */
/* ============================================================================================== */

export const normalize = (size: number, forHeight?: boolean) => {
  const newSize = size * (forHeight ? scaleHeight : scale);
  return Math.round(PixelRatio.roundToNearestPixel(newSize));
};

export const normalizeFont = (size: number) => {
  const scaledSize = size * scale * PixelRatio.getFontScale();
  return clamp(Math.round(PixelRatio.roundToNearestPixel(scaledSize)), 10, 40);
};

/**
 * Clamps a value between min and max
 */
const clamp = (value: number, min: number, max: number) =>
  Math.min(Math.max(value, min), max);

/* ============================================================================================== */
/*                                            CONSTANTS                                           */
/* ============================================================================================== */

const isSmallDevice = SCREEN_WIDTH < 375 || SCREEN_HEIGHT < 670;

// Brand colors (consistent across themes)
export const brandColors = {
  main: '#633c61',
  mainLight: '#7a4d74',
  coral: '#eb634b',
  coralLight: '#ee5339',
} as const;

// Base colors
export const baseColors = {
  white: '#FFFFFF',
  black: '#000000',
  transparent: 'transparent',
} as const;

// Neutral palette
export const neutralColors = {
  50: '#FAFAFA',
  100: '#F5F5F5',
  200: '#EEEEEE',
  300: '#E0E0E0',
  400: '#BDBDBD',
  500: '#9E9E9E',
  600: '#757575',
  700: '#616161',
  800: '#424242',
  900: '#212121',
} as const;

// Light theme colors
export const lightThemeColors: ThemeColors = {
  // Background colors
  background: baseColors.white,
  backgroundSecondary: neutralColors[50],
  backgroundTertiary: neutralColors[100],
  surface: baseColors.white,
  surfaceSecondary: neutralColors[50],

  // Text colors
  text: neutralColors[900],
  textSecondary: neutralColors[700],
  textTertiary: neutralColors[500],
  textInverse: baseColors.white,

  // Brand colors
  primary: brandColors.main,
  primaryLight: brandColors.mainLight,
  primaryDark: '#4a2d47',
  secondary: brandColors.coral,
  secondaryLight: brandColors.coralLight,
  secondaryDark: '#d4472f',

  // Status colors
  success: '#34C759',
  warning: '#FF9500',
  error: '#FF3B30',
  info: '#007AFF',

  // Interactive colors
  border: neutralColors[300],
  borderSecondary: neutralColors[200],
  borderFocus: brandColors.main,
  shadow: 'rgba(0, 0, 0, 0.1)',
  overlay: 'rgba(0, 0, 0, 0.5)',

  // Special colors
  accent: brandColors.coral,
  disabled: neutralColors[400],
  placeholder: neutralColors[500],
};

// Dark theme colors
export const darkThemeColors: ThemeColors = {
  // Background colors
  background: baseColors.black,
  backgroundSecondary: '#111111',
  backgroundTertiary: '#1C1C1E',
  surface: '#1C1C1E',
  surfaceSecondary: '#2C2C2E',

  // Text colors
  text: baseColors.white,
  textSecondary: neutralColors[300],
  textTertiary: neutralColors[500],
  textInverse: baseColors.black,

  // Brand colors
  primary: brandColors.mainLight,
  primaryLight: '#8a5d84',
  primaryDark: brandColors.main,
  secondary: brandColors.coralLight,
  secondaryLight: '#f16b53',
  secondaryDark: brandColors.coral,

  // Status colors
  success: '#30D158',
  warning: '#FF9F0A',
  error: '#FF453A',
  info: '#0A84FF',

  // Interactive colors
  border: '#3A3A3C',
  borderSecondary: '#2C2C2E',
  borderFocus: brandColors.mainLight,
  shadow: 'rgba(0, 0, 0, 0.3)',
  overlay: 'rgba(0, 0, 0, 0.7)',

  // Special colors
  accent: brandColors.coralLight,
  disabled: neutralColors[600],
  placeholder: neutralColors[500],
};

// Legacy colors object for backward compatibility
export const colors = {
  brand: brandColors,
  light: {
    background: lightThemeColors.background,
    text: lightThemeColors.text,
    primary: lightThemeColors.primary,
    secondary: lightThemeColors.secondary,
    border: lightThemeColors.border,
  },
  dark: {
    background: darkThemeColors.background,
    text: darkThemeColors.text,
    primary: darkThemeColors.primary,
    secondary: darkThemeColors.secondary,
    border: darkThemeColors.border,
  },
  base: baseColors,
  neutral: {
    500: neutralColors[500],
  },
};

export const fonts = {
  default: 'SF-Pro',
};

export const typography = {
  xs: normalizeFont(12),
  sm: normalizeFont(16),
  md: normalizeFont(22),
  lg: normalizeFont(26),
  xl: normalizeFont(30),
  xxl: normalizeFont(34),
};

export const spacing = {
  sm: normalize(8),
  md: normalize(12),
  lg: normalize(16),
  xl: normalize(24),
  xxl: normalize(32),
  xxxl: normalize(40),
} as const;

export const layout = {
  images: {
    xs: normalize(40),
    sm: normalize(80),
    md: normalize(150),
    lg: normalize(200),
  },
  ph: {
    sm: normalize(16),
    md: normalize(32),
    lg: normalize(48),
    screen: isSmallDevice ? spacing.md : spacing.lg,
  },
  pv: {
    sm: normalize(12),
    md: normalize(24),
    lg: normalize(36),
    screen: isSmallDevice ? spacing.sm : spacing.sm,
  },
  gap: {
    screen: spacing.lg,
  },
  borderRadius: {
    sm: normalize(8),
    md: normalize(14),
    lg: normalize(20),
  },
} as const;

// Create theme objects
export const lightTheme: Theme = {
  colors: lightThemeColors,
  mode: 'light',
  fonts,
  typography,
  spacing,
  layout,
};

export const darkTheme: Theme = {
  colors: darkThemeColors,
  mode: 'dark',
  fonts,
  typography,
  spacing,
  layout,
};

// Theme utilities
export const getTheme = (mode: ColorScheme): Theme => {
  return mode === 'dark' ? darkTheme : lightTheme;
};

export const getThemeColors = (mode: ColorScheme): ThemeColors => {
  return mode === 'dark' ? darkThemeColors : lightThemeColors;
};

// Default export for backward compatibility
export default {
  SCREEN_WIDTH,
  SCREEN_HEIGHT,
  isSmallDevice,
  colors,
  fonts,
  typography,
  spacing,
  layout,
  lightTheme,
  darkTheme,
  getTheme,
  getThemeColors,
};
